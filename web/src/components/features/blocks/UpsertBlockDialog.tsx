import { Button } from "@/components/ui/Button";
import { Dialog } from "@/components/ui/Dialog";
import {
  BLOCK_TYPE,
  upsertBlockSchema,
  useFolderStore,
  type Block,
  type Note,
  type UpsertBlockFormData,
} from "@/stores/useFolderStore";
import { useState } from "react";
import { TextEditor } from "@/components/features/editor/TextEditor";
import type { EditorState } from "lexical";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

export const UpsertBlockDialog = ({
  open,
  onOpenChange,
  note,
  block,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  note: Note;
  block?: Block;
}) => {
  const folderStore = useFolderStore();
  const [blockType, setBlockType] = useState<BLOCK_TYPE | undefined>(
    block?.block_type?.name ? BLOCK_TYPE[block?.block_type?.name] : undefined,
  );
  const [blockContent, setBlockContent] = useState<string | undefined>(block?.content ?? undefined);

  const dialogTitle = block?.id ? "Edit" : "Create";

  const form = useForm<UpsertBlockFormData>({
    resolver: zodResolver(upsertBlockSchema),
    values: {
      id: block?.id ?? undefined,
      title: block?.title ?? "",
      content: blockContent,
      blockType: blockType ?? BLOCK_TYPE.TEXT,
      noteId: note.id,
    },
    mode: "all",
  });

  const handleChange = (editorState: EditorState) => {
    setBlockContent(JSON.stringify(editorState.toJSON()));
  };

  const onCreate = async () => {
    await folderStore.upsertBlock(form.getValues());
    setBlockType(undefined);
    onOpenChange(false);
  };

  const handleOpenChange = () => {
    setBlockType(undefined);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <Dialog.Content className="max-w-[350px] sm:max-w-[725px]">
        <Dialog.Header>
          <Dialog.Title>
            {dialogTitle} block for {note.title}
          </Dialog.Title>
        </Dialog.Header>

        <Dialog.Description className="text-sm text-muted-foreground mb-4">
          Blocks are the building blocks of your notes. You can add text, statblocks, and more.
        </Dialog.Description>

        {!blockType ? (
          <div className="w-full flex items-center justify-center">
            <div className="flex flex-col gap-2 max-w-sm w-full">
              <Button variant="outline" onClick={() => setBlockType(BLOCK_TYPE.TEXT)}>
                Text
              </Button>
              <Button variant="outline" onClick={() => setBlockType(BLOCK_TYPE.STATBLOCK)}>
                Statblock
              </Button>
            </div>
          </div>
        ) : null}

        {blockType === BLOCK_TYPE.TEXT ? <TextEditor onChange={handleChange} /> : null}

        <Dialog.Footer>
          <Dialog.Close asChild>
            <Button variant="outline">Cancel</Button>
          </Dialog.Close>
          <Button type="submit" onClick={() => onCreate()}>
            Create
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};
