import { Button } from "@/components/ui/Button";
import { BLOCK_TYPE, type Note } from "@/stores/useFolderStore";
import { PlusCircle, SquarePen } from "lucide-react";
import { useState } from "react";
import { CreateNoteDialog } from "./CreateNoteDialog";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/Tooltip";
import { UpsertBlockDialog } from "../blocks/UpsertBlockDialog";
import { TextBlockDisplay } from "../blocks/TextBlockDisplay";

export const EditNote = ({ note }: { note: Note }) => {
  const [createNoteDialogOpen, setCreateNoteDialogOpen] = useState(false);
  const [upsertBlockDialogOpen, setUpsertBlockDialogOpen] = useState(false);

  console.log(
    note.blocks.map((block) => (block.block_type.name as BLOCK_TYPE) === BLOCK_TYPE.TEXT),
  );
  return (
    <>
      <div className="flex flex-col gap-4">
        <div className="w-full">
          <div className="w-full flex justify-between items-center">
            <h1 className="scroll-m-20 text-4xl font-extrabold tracking-tight text-balance">
              {note.title}
            </h1>

            <div className="flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="size-8"
                    onClick={() => setCreateNoteDialogOpen(true)}
                  >
                    <SquarePen />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Edit note</TooltipContent>
              </Tooltip>
            </div>
          </div>

          <p className="text-muted-foreground text-sm">{note.content}</p>
        </div>

        <hr className="w-full" />

        {note.blocks.map((block) =>
          (block.name as BLOCK_TYPE) === BLOCK_TYPE.TEXT ? (
            <TextBlockDisplay key={block.id} block={block} />
          ) : null,
        )}

        <Button variant="secondary" onClick={() => setUpsertBlockDialogOpen(true)}>
          <PlusCircle /> Add block
        </Button>
      </div>

      <CreateNoteDialog
        open={createNoteDialogOpen}
        onOpenChange={setCreateNoteDialogOpen}
        noteId={note.id}
        title={note.title}
        content={note.content ?? undefined}
        folderId={note.folder_id ?? undefined}
      />

      <UpsertBlockDialog
        open={upsertBlockDialogOpen}
        onOpenChange={setUpsertBlockDialogOpen}
        note={note}
      />
    </>
  );
};
